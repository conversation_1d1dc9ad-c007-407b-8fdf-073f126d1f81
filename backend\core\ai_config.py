#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI服务配置

管理AI服务的相关配置参数
"""
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from backend.core.conf import settings


class AIServiceConfig(BaseModel):
    """AI服务配置模型"""
    
    # 远程AI服务配置
    base_url: str = Field(default="http://*************:8888/v1", description="AI服务基础URL")
    api_key: Optional[str] = Field(default=None, description="API密钥")
    default_model: str = Field(default="Qwen3-32B-AWQ", description="默认AI模型")
    max_tokens: int = Field(default=4096, description="最大token数量")
    timeout: float = Field(default=60.0, description="请求超时时间(秒)")
    
    # 聊天参数配置
    default_temperature: float = Field(default=0.7, description="默认生成温度")
    default_top_p: float = Field(default=0.9, description="默认Top-p采样参数")
    max_history_messages: int = Field(default=20, description="最大历史消息数量")
    
    # 系统提示词配置
    system_prompt: str = Field(
        default="你是一个专业的AI助手，能够提供准确、有用的回答。请用中文回复用户的问题。",
        description="系统提示词"
    )
    
    # 健康检查配置
    health_check_interval: int = Field(default=300, description="健康检查间隔(秒)")
    health_check_timeout: float = Field(default=10.0, description="健康检查超时时间(秒)")
    
    # 错误重试配置
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟时间(秒)")
    
    # 流式响应配置
    stream_chunk_size: int = Field(default=1024, description="流式响应块大小")
    stream_timeout: float = Field(default=120.0, description="流式响应超时时间(秒)")


class AIModelConfig(BaseModel):
    """AI模型配置"""
    
    name: str = Field(..., description="模型名称")
    display_name: str = Field(..., description="显示名称")
    description: str = Field(..., description="模型描述")
    max_tokens: int = Field(..., description="最大token数量")
    supports_stream: bool = Field(default=True, description="是否支持流式响应")
    supports_function_call: bool = Field(default=False, description="是否支持函数调用")
    cost_per_token: float = Field(default=0.0, description="每token成本")
    status: str = Field(default="available", description="模型状态")


# 全局AI服务配置实例
ai_config = AIServiceConfig()

# 预定义的AI模型配置
AVAILABLE_MODELS: Dict[str, AIModelConfig] = {
    "Qwen3-32B-AWQ": AIModelConfig(
        name="Qwen3-32B-AWQ",
        display_name="通义千问3-32B",
        description="阿里巴巴通义千问3代32B参数模型，支持中英文对话，上下文长度40K",
        max_tokens=40960,
        supports_stream=True,
        supports_function_call=True,
        cost_per_token=0.0,
        status="available"
    ),
    "Qwen2.5-72B": AIModelConfig(
        name="Qwen2.5-72B",
        display_name="通义千问2.5-72B",
        description="阿里巴巴通义千问2.5代72B参数模型，更强的推理能力",
        max_tokens=32768,
        supports_stream=True,
        supports_function_call=True,
        cost_per_token=0.0,
        status="available"
    )
}


def get_ai_config() -> AIServiceConfig:
    """
    获取AI服务配置
    
    Returns:
        AIServiceConfig: AI服务配置实例
    """
    return ai_config


def get_model_config(model_name: str) -> Optional[AIModelConfig]:
    """
    获取指定模型的配置
    
    Args:
        model_name: 模型名称
        
    Returns:
        AIModelConfig: 模型配置，如果不存在则返回None
    """
    return AVAILABLE_MODELS.get(model_name)


def get_available_models() -> Dict[str, AIModelConfig]:
    """
    获取所有可用模型配置
    
    Returns:
        Dict[str, AIModelConfig]: 可用模型配置字典
    """
    return AVAILABLE_MODELS.copy()


def update_ai_config(**kwargs) -> None:
    """
    更新AI服务配置
    
    Args:
        **kwargs: 要更新的配置参数
    """
    global ai_config
    
    for key, value in kwargs.items():
        if hasattr(ai_config, key):
            setattr(ai_config, key, value)


def validate_model_parameters(
    model_name: str,
    max_tokens: Optional[int] = None,
    temperature: Optional[float] = None,
    top_p: Optional[float] = None
) -> Dict[str, Any]:
    """
    验证和标准化模型参数
    
    Args:
        model_name: 模型名称
        max_tokens: 最大token数量
        temperature: 生成温度
        top_p: Top-p采样参数
        
    Returns:
        Dict[str, Any]: 验证后的参数
        
    Raises:
        ValueError: 参数验证失败
    """
    model_config = get_model_config(model_name)
    if not model_config:
        raise ValueError(f"不支持的模型: {model_name}")
    
    # 验证max_tokens
    if max_tokens is None:
        max_tokens = ai_config.max_tokens
    elif max_tokens > model_config.max_tokens:
        raise ValueError(f"max_tokens ({max_tokens}) 超过模型最大限制 ({model_config.max_tokens})")
    elif max_tokens <= 0:
        raise ValueError("max_tokens 必须大于0")
    
    # 验证temperature
    if temperature is None:
        temperature = ai_config.default_temperature
    elif not (0.0 <= temperature <= 2.0):
        raise ValueError("temperature 必须在0.0到2.0之间")
    
    # 验证top_p
    if top_p is None:
        top_p = ai_config.default_top_p
    elif not (0.0 <= top_p <= 1.0):
        raise ValueError("top_p 必须在0.0到1.0之间")
    
    return {
        "model": model_name,
        "max_tokens": max_tokens,
        "temperature": temperature,
        "top_p": top_p
    }


def get_system_prompt(custom_prompt: Optional[str] = None) -> str:
    """
    获取系统提示词
    
    Args:
        custom_prompt: 自定义提示词
        
    Returns:
        str: 系统提示词
    """
    if custom_prompt:
        return custom_prompt
    return ai_config.system_prompt


def format_chat_history_for_api(messages: list, max_messages: Optional[int] = None) -> list:
    """
    格式化聊天历史为API调用格式
    
    Args:
        messages: 聊天消息列表
        max_messages: 最大消息数量限制
        
    Returns:
        list: 格式化后的消息列表
    """
    if max_messages is None:
        max_messages = ai_config.max_history_messages
    
    # 只保留最近的消息
    recent_messages = messages[-max_messages:] if len(messages) > max_messages else messages
    
    # 转换为API格式
    api_messages = []
    for msg in recent_messages:
        if hasattr(msg, 'role') and hasattr(msg, 'content'):
            api_messages.append({
                "role": msg.role,
                "content": msg.content
            })
    
    return api_messages
