#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具管理API

提供工具的查询、管理和执行相关的API接口
"""
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from loguru import logger

from backend.app.iot.service.tool_service import get_tool_service
from backend.app.iot.schema.agent import ToolCallInfo
from backend.common.response.response_schema import ResponseSchemaModel, response_base
from backend.common.security.jwt import DependsJwtAuth
from backend.database.db import CurrentSession


router = APIRouter()
tool_service = get_tool_service()


@router.get("/tools", summary="获取工具列表", response_model=ResponseSchemaModel[List[Dict[str, Any]]])
async def get_tools(
    db: CurrentSession,
    current_user = DependsJwtAuth,
    category: Optional[str] = Query(None, description="工具分类过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    limit: int = Query(50, description="返回数量限制", ge=1, le=100)
):
    """
    获取可用工具列表
    
    支持按分类、状态过滤，以及关键词搜索
    """
    try:
        # 获取工具列表（简化实现）
        tools = await tool_service.get_available_tools(category=category)

        # 搜索过滤
        if search:
            search_lower = search.lower()
            tools = [
                tool for tool in tools
                if search_lower in tool.get("name", "").lower()
                or search_lower in tool.get("description", "").lower()
            ]

        tools = tools[:limit]
        
        return await response_base.success(data=tools)
        
    except Exception as e:
        logger.error(f"获取工具列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工具列表失败: {str(e)}"
        )


@router.get("/tools/{tool_name}", summary="获取工具详情", response_model=ResponseSchemaModel[Dict[str, Any]])
async def get_tool_info(
    tool_name: str,
    db: CurrentSession,
    current_user = DependsJwtAuth
):
    """获取指定工具的详细信息"""
    try:
        tool_info = await tool_service.get_tool_info(tool_name)
        
        if not tool_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"工具不存在: {tool_name}"
            )
        
        return await response_base.success(data=tool_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取工具信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工具信息失败: {str(e)}"
        )


@router.post("/tools/{tool_name}/execute", summary="执行工具", response_model=ResponseSchemaModel[ToolCallInfo])
async def execute_tool(
    tool_name: str,
    inputs: Dict[str, Any],
    db: CurrentSession,
    current_user = DependsJwtAuth,
    timeout: Optional[float] = Query(None, description="超时时间(秒)", ge=1, le=300)
):
    """
    执行指定工具
    
    提供工具的直接执行接口，主要用于测试和调试
    """
    try:
        # 执行工具（简化实现，不需要复杂的上下文）
        result = await tool_service.execute_tool(
            tool_name=tool_name,
            inputs=inputs,
            timeout=timeout
        )
        
        return await response_base.success(data=result)
        
    except Exception as e:
        logger.error(f"执行工具失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"执行工具失败: {str(e)}"
        )


@router.post("/tools/batch-execute", summary="批量执行工具", response_model=ResponseSchemaModel[List[ToolCallInfo]])
async def batch_execute_tools(
    tool_configs: List[Dict[str, Any]],
    db: CurrentSession,
    current_user = DependsJwtAuth,
    max_parallel: int = Query(1, description="最大并行数", ge=1, le=5)
):
    """
    批量执行工具
    
    支持串行或并行执行多个工具
    """
    try:
        # 验证工具配置
        for config in tool_configs:
            if "tool_name" not in config:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="工具配置缺少tool_name字段"
                )
        
        # 批量执行工具（简化实现）
        results = []
        for config in tool_configs:
            tool_name = config["tool_name"]
            inputs = config.get("inputs", {})
            timeout = config.get("timeout")

            result = await tool_service.execute_tool(
                tool_name=tool_name,
                inputs=inputs,
                timeout=timeout
            )
            results.append(result)
        
        return await response_base.success(data=results)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量执行工具失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量执行工具失败: {str(e)}"
        )


@router.get("/categories", summary="获取工具分类", response_model=ResponseSchemaModel[List[Dict[str, Any]]])
async def get_tool_categories(
    db: CurrentSession,
    current_user = DependsJwtAuth
):
    """获取所有工具分类及其统计信息"""
    try:
        # 获取所有工具
        all_tools = await tool_service.get_available_tools()
        
        # 按分类统计
        category_stats = {}
        for tool in all_tools:
            category = tool["category"]
            if category not in category_stats:
                category_stats[category] = {
                    "category": category,
                    "display_name": category.replace("_", " ").title(),
                    "tool_count": 0,
                    "available_count": 0
                }
            
            category_stats[category]["tool_count"] += 1
            if tool["status"] == "available":
                category_stats[category]["available_count"] += 1
        
        categories = list(category_stats.values())
        
        return await response_base.success(data=categories)
        
    except Exception as e:
        logger.error(f"获取工具分类失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工具分类失败: {str(e)}"
        )


@router.get("/stats", summary="获取工具统计信息", response_model=ResponseSchemaModel[Dict[str, Any]])
async def get_tool_stats(
    db: CurrentSession,
    current_user = DependsJwtAuth
):
    """获取工具服务的统计信息"""
    try:
        stats = tool_service.get_service_stats()
        
        return await response_base.success(data=stats)
        
    except Exception as e:
        logger.error(f"获取工具统计信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工具统计信息失败: {str(e)}"
        )


@router.get("/executions/{execution_id}/status", summary="获取执行状态")
async def get_execution_status(
    execution_id: str,
    db: CurrentSession,
    current_user = DependsJwtAuth
):
    """获取工具执行状态"""
    try:
        status = await tool_service.get_execution_status(execution_id)
        
        if status is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"执行记录不存在: {execution_id}"
            )
        
        return await response_base.success(data={"execution_id": execution_id, "status": status})
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取执行状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取执行状态失败: {str(e)}"
        )


@router.post("/executions/{execution_id}/cancel", summary="取消执行")
async def cancel_execution(
    execution_id: str,
    db: CurrentSession,
    current_user = DependsJwtAuth
):
    """取消正在执行的工具"""
    try:
        success = await tool_service.cancel_execution(execution_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"执行记录不存在或已完成: {execution_id}"
            )
        
        return await response_base.success(message="执行已取消")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消执行失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消执行失败: {str(e)}"
        )
