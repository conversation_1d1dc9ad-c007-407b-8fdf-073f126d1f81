#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能体相关数据模型

定义智能体聊天、工具调用等相关的Pydantic模型
"""
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum

# 移除对已删除模块的依赖


class AgentMode(str, Enum):
    """智能体模式"""
    DIRECT = "direct"           # 直接回复模式
    TOOL_ASSISTED = "tool_assisted"  # 工具辅助模式
    AUTO = "auto"              # 自动选择模式


class ConversationStatus(str, Enum):
    """对话状态"""
    ACTIVE = "active"          # 活跃
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"         # 失败
    TIMEOUT = "timeout"       # 超时


class ToolCallStatus(str, Enum):
    """工具调用状态"""
    PENDING = "pending"        # 等待中
    RUNNING = "running"        # 执行中
    SUCCESS = "success"        # 成功
    FAILED = "failed"         # 失败
    TIMEOUT = "timeout"       # 超时


class AgentConfig(BaseModel):
    """智能体配置"""
    
    # 基础配置
    agent_mode: AgentMode = Field(AgentMode.AUTO, description="智能体模式")
    max_tool_calls: int = Field(5, description="最大工具调用次数", ge=1, le=20)
    tool_call_timeout: float = Field(30.0, description="工具调用超时时间(秒)", ge=1.0, le=300.0)
    
    # 工具选择配置
    enable_tool_chaining: bool = Field(True, description="是否启用工具链式调用")
    tool_selection_strategy: str = Field("ai_based", description="工具选择策略")
    similarity_threshold: float = Field(0.7, description="工具匹配相似度阈值", ge=0.0, le=1.0)
    
    # 安全配置
    allowed_tool_categories: List[str] = Field(
        default=["query", "search", "analysis"], 
        description="允许的工具分类"
    )
    require_confirmation: bool = Field(False, description="是否需要用户确认工具调用")
    
    # 响应配置
    include_tool_details: bool = Field(True, description="是否在响应中包含工具执行详情")
    max_response_length: int = Field(4000, description="最大响应长度", ge=100, le=10000)


class AgentChatRequest(BaseModel):
    """智能体聊天请求"""
    
    message: str = Field(..., description="用户消息", min_length=1, max_length=4000)
    session_id: str = Field(..., description="会话ID")
    agent_config: Optional[AgentConfig] = Field(None, description="智能体配置")
    context: Optional[Dict[str, Any]] = Field(None, description="额外上下文信息")
    
    # 继承自原有聊天请求的字段
    model_name: str = Field("Qwen2.5-72B", description="使用的模型名称")
    system_prompt: Optional[str] = Field(None, description="系统提示")
    temperature: float = Field(0.7, description="温度参数", ge=0.0, le=2.0)
    max_tokens: int = Field(2000, description="最大token数", ge=1, le=4000)
    stream: bool = Field(False, description="是否流式响应")


class ToolCallInfo(BaseModel):
    """工具调用信息"""
    
    tool_name: str = Field(..., description="工具名称")
    tool_display_name: str = Field(..., description="工具显示名称")
    inputs: Dict[str, Any] = Field(..., description="输入参数")
    status: ToolCallStatus = Field(..., description="调用状态")
    start_time: datetime = Field(..., description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    execution_time: float = Field(0.0, description="执行时间(秒)")
    outputs: Optional[Dict[str, Any]] = Field(None, description="输出结果")
    error_message: Optional[str] = Field(None, description="错误信息")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")


class AgentExecutionResult(BaseModel):
    """智能体执行结果"""
    
    success: bool = Field(..., description="执行是否成功")
    response: str = Field(..., description="最终回复内容")
    tool_calls: List[ToolCallInfo] = Field(default_factory=list, description="工具调用列表")
    execution_summary: str = Field(..., description="执行摘要")
    total_execution_time: float = Field(0.0, description="总执行时间(秒)")
    error_message: Optional[str] = Field(None, description="错误信息")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")


class AgentChatResponse(BaseModel):
    """智能体聊天响应"""
    
    session_id: str = Field(..., description="会话ID")
    message_id: str = Field(..., description="消息ID")
    content: str = Field(..., description="回复内容")
    agent_mode: AgentMode = Field(..., description="使用的智能体模式")
    execution_result: AgentExecutionResult = Field(..., description="执行结果详情")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
    
    # 兼容原有聊天响应
    model_name: str = Field(..., description="使用的模型名称")
    usage: Optional[Dict[str, int]] = Field(None, description="token使用情况")


class AgentStreamResponse(BaseModel):
    """智能体流式响应"""
    
    session_id: str = Field(..., description="会话ID")
    message_id: str = Field(..., description="消息ID")
    event_type: str = Field(..., description="事件类型")
    data: Dict[str, Any] = Field(..., description="事件数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="事件时间")


class ConversationContext(BaseModel):
    """对话上下文"""
    
    session_id: str = Field(..., description="会话ID")
    user_id: int = Field(..., description="用户ID")
    conversation_history: List[Dict[str, Any]] = Field(default_factory=list, description="对话历史")
    tool_execution_history: List[ToolCallInfo] = Field(default_factory=list, description="工具执行历史")
    global_context: Dict[str, Any] = Field(default_factory=dict, description="全局上下文")
    agent_config: AgentConfig = Field(default_factory=AgentConfig, description="智能体配置")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class ToolSelectionRequest(BaseModel):
    """工具选择请求"""
    
    user_message: str = Field(..., description="用户消息")
    available_tools: List[str] = Field(..., description="可用工具列表")
    context: Optional[Dict[str, Any]] = Field(None, description="上下文信息")
    selection_strategy: str = Field("ai_based", description="选择策略")


class ToolSelectionResponse(BaseModel):
    """工具选择响应"""
    
    need_tools: bool = Field(..., description="是否需要工具")
    selected_tools: List[Dict[str, Any]] = Field(default_factory=list, description="选中的工具")
    execution_plan: str = Field("", description="执行计划说明")
    confidence_score: float = Field(0.0, description="置信度分数", ge=0.0, le=1.0)
    reasoning: str = Field("", description="选择理由")


class AgentSessionInfo(BaseModel):
    """智能体会话信息"""
    
    session_id: str = Field(..., description="会话ID")
    user_id: int = Field(..., description="用户ID")
    status: ConversationStatus = Field(..., description="会话状态")
    agent_config: AgentConfig = Field(..., description="智能体配置")
    message_count: int = Field(0, description="消息数量")
    tool_call_count: int = Field(0, description="工具调用次数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    last_activity: datetime = Field(..., description="最后活动时间")


class AgentStats(BaseModel):
    """智能体统计信息"""
    
    total_conversations: int = Field(0, description="总对话数")
    active_conversations: int = Field(0, description="活跃对话数")
    total_tool_calls: int = Field(0, description="总工具调用次数")
    successful_tool_calls: int = Field(0, description="成功工具调用次数")
    average_response_time: float = Field(0.0, description="平均响应时间(秒)")
    most_used_tools: List[Dict[str, Any]] = Field(default_factory=list, description="最常用工具")
    error_rate: float = Field(0.0, description="错误率", ge=0.0, le=1.0)


# 工具结果转换函数已移除，使用LangChain标准工具调用

# 简化的工具调用信息创建函数
def create_tool_call_info(
    tool_name: str,
    arguments: dict,
    result: str,
    success: bool = True,
    execution_time: float = 0.0
) -> ToolCallInfo:
    """创建工具调用信息"""
    now = datetime.now()
    return ToolCallInfo(
        tool_name=tool_name,
        tool_display_name=tool_name,
        inputs=arguments,
        status=ToolCallStatus.SUCCESS if success else ToolCallStatus.FAILED,
        start_time=now,
        end_time=now,
        execution_time=execution_time,
        outputs={"result": result} if success else None,
        error_message=result if not success else None,
        metadata={}
    )
