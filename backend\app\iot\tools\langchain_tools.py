#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangChain工具注册中心

使用LangChain标准@tool装饰器的工具集合
"""
from typing import List
from langchain_core.tools import BaseTool

# 导入所有工具
from backend.app.iot.tools.calculator import calculator, advanced_calculator
from backend.app.iot.tools.knowledge_query import knowledge_query


def get_all_langchain_tools() -> List[BaseTool]:
    """
    获取所有LangChain工具
    
    Returns:
        List[BaseTool]: 所有可用的LangChain工具列表
    """
    tools = [
        calculator,
        advanced_calculator,
        knowledge_query,
    ]
    
    return tools


def get_tool_by_name(tool_name: str) -> BaseTool:
    """
    根据名称获取工具
    
    Args:
        tool_name: 工具名称
        
    Returns:
        BaseTool: 对应的工具实例
        
    Raises:
        ValueError: 如果工具不存在
    """
    tools = get_all_langchain_tools()
    
    for tool in tools:
        if tool.name == tool_name:
            return tool
    
    raise ValueError(f"工具 '{tool_name}' 不存在")


def get_tools_by_category(category: str) -> List[BaseTool]:
    """
    根据分类获取工具
    
    Args:
        category: 工具分类
        
    Returns:
        List[BaseTool]: 该分类下的工具列表
    """
    all_tools = get_all_langchain_tools()
    
    # 根据工具名称进行简单分类
    category_mapping = {
        "math": ["calculator", "advanced_calculator"],
        "knowledge": ["knowledge_query"],
        "utility": ["calculator", "advanced_calculator"],
        "query": ["knowledge_query"]
    }
    
    if category not in category_mapping:
        return []
    
    tool_names = category_mapping[category]
    return [tool for tool in all_tools if tool.name in tool_names]


def get_tool_info() -> List[dict]:
    """
    获取所有工具的信息
    
    Returns:
        List[dict]: 工具信息列表
    """
    tools = get_all_langchain_tools()
    tool_info = []
    
    for tool in tools:
        info = {
            "name": tool.name,
            "description": tool.description,
            "args_schema": tool.args_schema.schema() if tool.args_schema else None,
        }
        tool_info.append(info)
    
    return tool_info


# 工具分类常量
TOOL_CATEGORIES = {
    "MATH": "math",
    "KNOWLEDGE": "knowledge", 
    "UTILITY": "utility",
    "QUERY": "query"
}
