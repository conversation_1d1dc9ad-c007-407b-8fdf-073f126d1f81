import request from '@/utils/request';
import { Session } from '/@/utils/storage';
import { fastApiRequest } from '/@/config/knowledgeBase';

// 工具调用类型定义
export interface ToolCall {
  id: string;
  name: string;
  arguments: Record<string, any>;
  result?: any;
  status: 'pending' | 'loading' | 'success' | 'error';
  error?: string;
  startTime?: number;
  endTime?: number;
}

// 智能体聊天请求参数
export interface AgentChatRequest {
  message: string;
  session_id?: string;
  mode?: 'auto' | 'ai_only' | 'agent';
  stream?: boolean;
}

// 智能体聊天响应
export interface AgentChatResponse {
  response: string;
  mode: 'auto' | 'ai_only' | 'agent';
  tool_calls?: ToolCall[];
  execution_summary?: string;
  session_id: string;
}

// 流式响应数据
export interface AgentStreamResponse {
  delta: string;
  mode?: 'auto' | 'ai_only' | 'agent';
  tool_calls?: ToolCall[];
  execution_summary?: string;
  session_id?: string;
  finished?: boolean;
}

/**
 * 智能体聊天 - 非流式
 */
export const chatWithAgent = async (params: AgentChatRequest): Promise<AgentChatResponse> => {
  return request({
    url: '/api/iot/v1/chat/chat',
    method: 'POST',
    data: {
      ...params,
      stream: false
    }
  });
};

/**
 * 智能体聊天 - 流式
 */
export const chatWithAgentStream = async (
  params: AgentChatRequest,
  onMessage: (response: AgentStreamResponse) => void,
  onError: (error: Error) => void,
  onComplete?: () => void
): Promise<void> => {
  try {
    // 获取认证token
    const token = Session.get('token');
    const authHeader = token ? (token.startsWith('Bearer ') ? token : `Bearer ${token}`) : '';

    const response = await fetch(`${fastApiRequest.defaults.baseURL}/api/iot/v1/chat/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Authorization': authHeader,
      },
      body: JSON.stringify({
        ...params,
        stream: true
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        onComplete?.();
        break;
      }

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (data === '[DONE]') {
            onComplete?.();
            return;
          }

          try {
            const parsed = JSON.parse(data) as AgentStreamResponse;
            onMessage(parsed);
          } catch (e) {
            console.warn('解析流式数据失败:', e, data);
          }
        }
      }
    }
  } catch (error) {
    console.error('智能体流式聊天错误:', error);
    onError(error as Error);
  }
};

/**
 * 获取可用工具列表
 */
export const getAvailableTools = async (): Promise<any[]> => {
  return request({
    url: '/api/iot/v1/tools/list',
    method: 'GET'
  });
};

/**
 * 生成会话ID
 */
export const generateAgentSessionId = (): string => {
  return `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
