#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
计算器工具

使用LangChain标准@tool装饰器的计算器工具
"""
import math
from typing import Optional
from langchain_core.tools import tool


@tool
def calculator(expression: str) -> str:
    """
    执行基本的数学计算，支持加法、减法、乘法、除法

    Args:
        expression: 要计算的数学表达式，例如: "2 + 3 * 4"

    Returns:
        str: 计算结果
    """
    try:
        expression = expression.strip()

        # 安全的数学表达式计算
        # 只允许基本的数学运算符和数字
        allowed_chars = set("0123456789+-*/.() ")
        if not all(c in allowed_chars for c in expression):
            return "错误：表达式包含不允许的字符，只支持数字和基本运算符 (+, -, *, /, (, ))"

        # 计算结果
        result = eval(expression)
        return f"计算结果: {result}"

    except ZeroDivisionError:
        return "错误：除零错误，不能除以零"
    except SyntaxError:
        return "错误：语法错误，请检查数学表达式的格式"
    except Exception as e:
        return f"计算失败: {str(e)}"


@tool
def advanced_calculator(operation: str, value: float, second_value: Optional[float] = None) -> str:
    """
    执行高级数学计算，支持三角函数、对数、指数等

    Args:
        operation: 运算类型，支持: sin, cos, tan, log, ln, sqrt, pow, abs
        value: 要计算的数值
        second_value: 某些运算需要的第二个数值（如幂运算）

    Returns:
        str: 计算结果
    """
    try:
        # 执行相应的数学运算
        if operation == "sin":
            result = math.sin(value)
        elif operation == "cos":
            result = math.cos(value)
        elif operation == "tan":
            result = math.tan(value)
        elif operation == "log":
            result = math.log10(value)
        elif operation == "ln":
            result = math.log(value)
        elif operation == "sqrt":
            result = math.sqrt(value)
        elif operation == "pow":
            if second_value is None:
                return "错误：幂运算需要提供第二个数值"
            result = math.pow(value, second_value)
        elif operation == "abs":
            result = abs(value)
        else:
            return f"错误：不支持的运算类型 {operation}，支持的运算: sin, cos, tan, log, ln, sqrt, pow, abs"

        return f"{operation}({value}{f', {second_value}' if second_value is not None else ''}) = {result}"

    except ValueError as e:
        return f"数值错误: {str(e)}"
    except Exception as e:
        return f"计算失败: {str(e)}"
