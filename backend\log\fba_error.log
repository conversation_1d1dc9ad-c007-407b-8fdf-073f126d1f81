2025-09-10 10:28:59.494 | ERROR    | 55abc599bcf5426d9cff46bea73af500 | Java token认证失败: 401: Token 格式错误
2025-09-10 10:39:47.299 | ERROR    | 54984ac4737b4e5e974b3233587ed7ed | 统一聊天失败: 'HTTPAuthorizationCredentials' object has no attribute 'id'
2025-09-10 10:39:49.347 | ERROR    | ec9c50299ca6469b96fc3fb56391cae9 | 统一聊天失败: 'HTTPAuthorizationCredentials' object has no attribute 'id'
2025-09-10 10:39:51.389 | ERROR    | c5932a72aeca4effafab42c84d9c4f88 | 统一聊天失败: 'HTTPAuthorizationCredentials' object has no attribute 'id'
2025-09-10 10:46:26.889 | ERROR    | 3542dc0775f0452f8450d38384460119 | 统一聊天失败: 'HTTPAuthorizationCredentials' object has no attribute 'id'
2025-09-10 10:46:28.940 | ERROR    | cca27205719846f48c5866fdeda3fe24 | 统一聊天失败: 'HTTPAuthorizationCredentials' object has no attribute 'id'
2025-09-10 10:46:30.998 | ERROR    | c88ab5ce1cf04300b201f9d184f4fb25 | 统一聊天失败: 'HTTPAuthorizationCredentials' object has no attribute 'id'
2025-09-10 10:47:26.006 | ERROR    | b24898462efa4663a51e2bba3ffd369f | 工具聊天失败: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 40960 tokens. However, you requested 41608 tokens (648 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-09-10 10:47:26.007 | ERROR    | b24898462efa4663a51e2bba3ffd369f | LangGraph智能体模式执行异常: 'AgentExecutionResult' object has no attribute 'execution_time'
2025-09-10 10:47:26.016 | ERROR    | b24898462efa4663a51e2bba3ffd369f | 统一聊天失败: object ResponseModel can't be used in 'await' expression
2025-09-10 10:47:28.073 | ERROR    | 4d614edf174f4e6a8d2d3d16d131f0df | 工具聊天失败: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 40960 tokens. However, you requested 41610 tokens (650 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-09-10 10:47:28.074 | ERROR    | 4d614edf174f4e6a8d2d3d16d131f0df | LangGraph智能体模式执行异常: 'AgentExecutionResult' object has no attribute 'execution_time'
2025-09-10 10:47:28.074 | ERROR    | 4d614edf174f4e6a8d2d3d16d131f0df | 统一聊天失败: object ResponseModel can't be used in 'await' expression
2025-09-10 10:47:30.501 | ERROR    | 1661401f64cd43049f4e169322fdf4aa | AI聊天失败: AI服务调用失败: 404 {"object":"error","message":"The model `Qwen2.5-72B` does not exist.","type":"NotFoundError","param":null,"code":404}
2025-09-10 10:47:30.889 | ERROR    | 1661401f64cd43049f4e169322fdf4aa | AI聊天失败: AI服务调用失败: 404 {"object":"error","message":"The model `Qwen2.5-72B` does not exist.","type":"NotFoundError","param":null,"code":404}
2025-09-10 10:47:30.889 | ERROR    | 1661401f64cd43049f4e169322fdf4aa | 统一聊天失败: AI聊天失败: AI服务调用失败: 404 {"object":"error","message":"The model `Qwen2.5-72B` does not exist.","type":"NotFoundError","param":null,"code":404}
2025-09-10 10:50:14.337 | ERROR    | 7416a49d20e4461c9bd7dc2b2fbba0c4 | 工具聊天失败: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 40960 tokens. However, you requested 41608 tokens (648 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-09-10 10:50:14.337 | ERROR    | 7416a49d20e4461c9bd7dc2b2fbba0c4 | LangGraph智能体模式执行异常: 'AgentExecutionResult' object has no attribute 'execution_time'
2025-09-10 10:50:14.337 | ERROR    | 7416a49d20e4461c9bd7dc2b2fbba0c4 | 统一聊天失败: object ResponseModel can't be used in 'await' expression
2025-09-10 10:50:16.428 | ERROR    | 8627fd1a179d4bb6a3499e8678e7dc76 | 工具聊天失败: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 40960 tokens. However, you requested 41610 tokens (650 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-09-10 10:50:16.429 | ERROR    | 8627fd1a179d4bb6a3499e8678e7dc76 | LangGraph智能体模式执行异常: 'AgentExecutionResult' object has no attribute 'execution_time'
2025-09-10 10:50:16.429 | ERROR    | 8627fd1a179d4bb6a3499e8678e7dc76 | 统一聊天失败: object ResponseModel can't be used in 'await' expression
2025-09-10 10:50:18.859 | ERROR    | 523576b059764124bd01b2cec9b3745e | AI聊天失败: AI服务调用失败: 404 {"object":"error","message":"The model `Qwen2.5-72B` does not exist.","type":"NotFoundError","param":null,"code":404}
2025-09-10 10:50:19.242 | ERROR    | 523576b059764124bd01b2cec9b3745e | AI聊天失败: AI服务调用失败: 404 {"object":"error","message":"The model `Qwen2.5-72B` does not exist.","type":"NotFoundError","param":null,"code":404}
2025-09-10 10:50:19.242 | ERROR    | 523576b059764124bd01b2cec9b3745e | 统一聊天失败: AI聊天失败: AI服务调用失败: 404 {"object":"error","message":"The model `Qwen2.5-72B` does not exist.","type":"NotFoundError","param":null,"code":404}
2025-09-10 10:53:28.324 | ERROR    | a10e535d08394683aaf6d9f07f0e5b01 | 工具聊天失败: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 40960 tokens. However, you requested 41608 tokens (648 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-09-10 10:53:28.326 | ERROR    | a10e535d08394683aaf6d9f07f0e5b01 | LangGraph智能体模式执行异常: 'AgentExecutionResult' object has no attribute 'execution_time'
2025-09-10 10:54:29.679 | ERROR    | 04509ae667754c68b310906b51c3f9fb | 工具聊天失败: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 40960 tokens. However, you requested 41608 tokens (648 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-09-10 10:54:29.681 | ERROR    | 04509ae667754c68b310906b51c3f9fb | LangGraph智能体模式执行异常: 'AgentExecutionResult' object has no attribute 'execution_time'
2025-09-10 10:56:45.305 | ERROR    | ad6a6e2f3902488fb6c97448ba593ac6 | 工具聊天失败: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 40960 tokens. However, you requested 41608 tokens (648 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-09-10 11:15:03.129 | ERROR    | 492673091b954ec9b21d448328296317 | LangGraph智能体执行失败: 'function'
2025-09-10 11:21:34.134 | ERROR    | f85082f92d904e7282e80ddae8bb68d8 | LangGraph智能体模式执行异常: 'ToolCallInfo' object has no attribute 'get'
2025-09-10 11:38:21.577 | ERROR    | 1eecdce4586d44c0b7788439f2385ebc | Java token认证失败: 401: Token 格式错误
2025-09-10 11:50:44.017 | ERROR    | 7c25b59efc0e493aa1c2e3cf96196c25 | AI聊天失败: 1 validation error for VLLMChatResponse
usage.prompt_tokens_details
  Input should be a valid integer [type=int_type, input_value=None, input_type=NoneType]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
2025-09-10 11:51:17.667 | ERROR    | 22515795463b4b6f9c10472a1be9ed11 | AI聊天失败: 1 validation error for VLLMChatResponse
usage.prompt_tokens_details
  Input should be a valid integer [type=int_type, input_value=None, input_type=NoneType]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
2025-09-10 13:05:53.428 | ERROR    | 2248a67f8f694cd5a005a7b3f23d083e | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:07:28.821 | ERROR    | 0dbc235111a0494396665184bc0ca30b | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:08:04.959 | ERROR    | f5d7b6bf5a6b4ebfb9e4d64dc5ec7329 | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:09:00.510 | ERROR    | 236d01cac2ef4e0cb2133068ce1a647e | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:09:30.684 | ERROR    | 20cebc703512403e9e35cc79f7395627 | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:11:27.707 | ERROR    | 89c7755904804f968bbfb3b43382f98d | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:12:36.489 | ERROR    | 6bbcbed288f046bfbf4e20137ea5ccee | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:13:28.011 | ERROR    | ee35a4c84b1641c6aae47a64fb5e0828 | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:14:30.706 | ERROR    | 952647a0953146c89165fa8b75b13353 | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:15:33.356 | ERROR    | d65c41d94b9940519f7c17c43eec84c6 | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:18:26.812 | ERROR    | e582aaba8b3944269a9c17f82d08b70e | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:20:25.633 | ERROR    | e45f1ee500074bbfa2828dcc7d37f4ab | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:21:29.888 | ERROR    | 3039a5311049424696b5f04f326653ea | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:24:33.017 | ERROR    | 5b9987cdaaa047ef85e8d5a11a403148 | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:28:29.915 | ERROR    | ff23655a2d724302bb00892f7bb9c506 | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:29:29.691 | ERROR    | 1096bcf4eada4d6db1e713e3fa7f464d | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:31:30.361 | ERROR    | 612bf675d7234af98b42765d6f2d19f0 | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:32:01.719 | ERROR    | 7957f4856e9b440a9871afcd2a641b6b | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
2025-09-10 13:33:32.741 | ERROR    | 49b6b472c53a4b2fa6fd04f4803e9d4f | RAGFlow检索异常: 400: 必须指定 dataset_ids 或 document_ids 中的至少一个参数
