#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket服务

提供WebSocket连接管理和实时通信功能
"""
import json
import asyncio
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from loguru import logger

from backend.app.iot.schema.agent import AgentStreamResponse


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接：{user_id: {session_id: websocket}}
        self.active_connections: Dict[int, Dict[str, WebSocket]] = {}
        # 用户会话映射：{user_id: set(session_ids)}
        self.user_sessions: Dict[int, Set[str]] = {}
        # 会话用户映射：{session_id: user_id}
        self.session_users: Dict[str, int] = {}
        # 连接统计
        self.connection_stats = {
            "total_connections": 0,
            "active_connections": 0,
            "total_messages": 0
        }
    
    async def connect(self, websocket: WebSocket, user_id: int, session_id: str):
        """建立WebSocket连接"""
        try:
            await websocket.accept()
            
            # 初始化用户连接字典
            if user_id not in self.active_connections:
                self.active_connections[user_id] = {}
                self.user_sessions[user_id] = set()
            
            # 如果该会话已有连接，先关闭旧连接
            if session_id in self.active_connections[user_id]:
                old_websocket = self.active_connections[user_id][session_id]
                try:
                    await old_websocket.close()
                except:
                    pass
            
            # 添加新连接
            self.active_connections[user_id][session_id] = websocket
            self.user_sessions[user_id].add(session_id)
            self.session_users[session_id] = user_id
            
            # 更新统计
            self.connection_stats["total_connections"] += 1
            self.connection_stats["active_connections"] = self._count_active_connections()
            
            logger.info(f"WebSocket连接建立: 用户{user_id}, 会话{session_id}")
            
            # 发送连接成功消息
            await self.send_to_session(session_id, {
                "event_type": "connection_established",
                "data": {
                    "user_id": user_id,
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat()
                }
            })
            
        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            raise
    
    async def disconnect(self, user_id: int, session_id: str):
        """断开WebSocket连接"""
        try:
            # 移除连接
            if user_id in self.active_connections:
                if session_id in self.active_connections[user_id]:
                    del self.active_connections[user_id][session_id]
                
                # 移除会话记录
                if session_id in self.user_sessions.get(user_id, set()):
                    self.user_sessions[user_id].remove(session_id)
                
                # 如果用户没有其他连接，清理用户记录
                if not self.active_connections[user_id]:
                    del self.active_connections[user_id]
                    if user_id in self.user_sessions:
                        del self.user_sessions[user_id]
            
            # 清理会话用户映射
            if session_id in self.session_users:
                del self.session_users[session_id]
            
            # 更新统计
            self.connection_stats["active_connections"] = self._count_active_connections()
            
            logger.info(f"WebSocket连接断开: 用户{user_id}, 会话{session_id}")
            
        except Exception as e:
            logger.error(f"WebSocket断开失败: {e}")
    
    async def send_to_session(self, session_id: str, message: Dict[str, Any]):
        """向指定会话发送消息"""
        try:
            user_id = self.session_users.get(session_id)
            if not user_id:
                logger.warning(f"会话不存在: {session_id}")
                return False
            
            websocket = self.active_connections.get(user_id, {}).get(session_id)
            if not websocket:
                logger.warning(f"WebSocket连接不存在: 用户{user_id}, 会话{session_id}")
                return False
            
            # 构建消息
            stream_response = AgentStreamResponse(
                session_id=session_id,
                message_id=message.get("message_id", ""),
                event_type=message.get("event_type", "message"),
                data=message.get("data", {}),
                timestamp=datetime.now()
            )
            
            # 发送消息
            await websocket.send_text(json.dumps(
                stream_response.dict(), 
                ensure_ascii=False, 
                default=str
            ))
            
            # 更新统计
            self.connection_stats["total_messages"] += 1
            
            return True
            
        except WebSocketDisconnect:
            logger.info(f"WebSocket连接已断开: 会话{session_id}")
            await self.disconnect(user_id, session_id)
            return False
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
            return False
    
    async def send_to_user(self, user_id: int, message: Dict[str, Any]):
        """向用户的所有会话发送消息"""
        try:
            if user_id not in self.user_sessions:
                logger.warning(f"用户无活跃会话: {user_id}")
                return 0
            
            sent_count = 0
            for session_id in self.user_sessions[user_id].copy():
                if await self.send_to_session(session_id, message):
                    sent_count += 1
            
            return sent_count
            
        except Exception as e:
            logger.error(f"向用户发送消息失败: {e}")
            return 0
    
    async def broadcast(self, message: Dict[str, Any], exclude_sessions: Optional[Set[str]] = None):
        """广播消息到所有连接"""
        try:
            exclude_sessions = exclude_sessions or set()
            sent_count = 0
            
            for session_id in self.session_users.keys():
                if session_id not in exclude_sessions:
                    if await self.send_to_session(session_id, message):
                        sent_count += 1
            
            logger.info(f"广播消息完成，发送到 {sent_count} 个会话")
            return sent_count
            
        except Exception as e:
            logger.error(f"广播消息失败: {e}")
            return 0
    
    def get_user_sessions(self, user_id: int) -> List[str]:
        """获取用户的活跃会话列表"""
        return list(self.user_sessions.get(user_id, set()))
    
    def is_session_active(self, session_id: str) -> bool:
        """检查会话是否活跃"""
        return session_id in self.session_users
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return {
            **self.connection_stats,
            "active_users": len(self.active_connections),
            "active_sessions": len(self.session_users)
        }
    
    def _count_active_connections(self) -> int:
        """计算活跃连接数"""
        count = 0
        for user_connections in self.active_connections.values():
            count += len(user_connections)
        return count
    
    async def cleanup_inactive_connections(self):
        """清理非活跃连接"""
        try:
            inactive_sessions = []
            
            for user_id, sessions in self.active_connections.items():
                for session_id, websocket in sessions.items():
                    try:
                        # 发送ping消息检查连接状态
                        await websocket.ping()
                    except:
                        inactive_sessions.append((user_id, session_id))
            
            # 清理非活跃连接
            for user_id, session_id in inactive_sessions:
                await self.disconnect(user_id, session_id)
            
            if inactive_sessions:
                logger.info(f"清理了 {len(inactive_sessions)} 个非活跃连接")
                
        except Exception as e:
            logger.error(f"清理连接失败: {e}")


# 全局连接管理器实例
connection_manager = ConnectionManager()


class WebSocketService:
    """WebSocket服务"""
    
    def __init__(self):
        self.manager = connection_manager
    
    async def handle_agent_conversation(
        self,
        websocket: WebSocket,
        user_id: int,
        session_id: str,
        message: str,
        agent_config: Optional[Dict[str, Any]] = None
    ):
        """处理智能体对话的WebSocket通信"""
        try:
            from backend.app.iot.service.langgraph_agent_service import LangGraphAgentService
            from backend.app.iot.schema.agent import AgentConfig

            # 发送开始事件
            await self.manager.send_to_session(session_id, {
                "event_type": "conversation_start",
                "data": {
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                }
            })

            # 创建智能体服务
            agent_service = LangGraphAgentService()

            # 解析智能体配置
            config = AgentConfig(**(agent_config or {}))

            # 发送工具选择开始事件
            await self.manager.send_to_session(session_id, {
                "event_type": "tool_selection_start",
                "data": {
                    "message": "正在分析您的问题并选择合适的工具..."
                }
            })

            # 执行智能体对话
            execution_result = await agent_service.execute_conversation(
                message=message,
                session_id=session_id,
                user_id=user_id,
                max_tool_calls=config.max_tool_calls
            )
            
            # 发送工具执行事件
            if execution_result.get("tool_calls"):
                for tool_call in execution_result["tool_calls"]:
                    await self.manager.send_to_session(session_id, {
                        "event_type": "tool_execution",
                        "data": {
                            "tool_name": tool_call.get("name", "unknown"),
                            "status": "success" if tool_call.get("success", False) else "failed",
                            "execution_time": tool_call.get("execution_time", 0.0),
                            "outputs": tool_call.get("result") if tool_call.get("success") else None,
                            "error": tool_call.get("error") if not tool_call.get("success") else None
                        }
                    })

            # 发送最终响应
            await self.manager.send_to_session(session_id, {
                "event_type": "response",
                "data": {
                    "content": execution_result.get("response", ""),
                    "execution_summary": execution_result.get("execution_summary", "")
                }
            })

            # 发送结束事件
            await self.manager.send_to_session(session_id, {
                "event_type": "conversation_end",
                "data": {
                    "success": execution_result.get("success", True),
                    "total_time": sum(
                        tool_call.get("execution_time", 0.0) for tool_call in execution_result.get("tool_calls", [])
                    )
                }
            })
            
        except Exception as e:
            logger.error(f"WebSocket智能体对话处理失败: {e}")
            await self.manager.send_to_session(session_id, {
                "event_type": "error",
                "data": {
                    "error_message": str(e),
                    "timestamp": datetime.now().isoformat()
                }
            })
    
    async def start_cleanup_task(self):
        """启动连接清理任务"""
        while True:
            try:
                await asyncio.sleep(300)  # 每5分钟清理一次
                await self.manager.cleanup_inactive_connections()
            except Exception as e:
                logger.error(f"连接清理任务异常: {e}")


# 全局WebSocket服务实例
websocket_service = WebSocketService()
