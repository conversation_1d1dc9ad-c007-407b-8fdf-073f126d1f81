#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangChain工具服务

提供LangChain工具的管理、执行、监控等功能
"""
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from loguru import logger

from backend.app.iot.tools.langchain_tools import (
    get_all_langchain_tools,
    get_tool_by_name,
    get_tools_by_category
)
from backend.app.iot.schema.agent import (
    ToolCallInfo,
    ToolCallStatus,
    create_tool_call_info
)


class ToolService:
    """
    LangChain工具服务
    
    提供LangChain工具的管理、执行、监控等功能
    """
    
    def __init__(self):
        self._execution_stats: Dict[str, Dict[str, Any]] = {}
        self._running_executions: Dict[str, asyncio.Task] = {}
    
    async def get_available_tools(
        self, 
        category: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取可用LangChain工具列表
        
        Args:
            category: 工具分类过滤
            
        Returns:
            List[Dict[str, Any]]: 工具信息列表
        """
        try:
            if category:
                tools = get_tools_by_category(category)
            else:
                tools = get_all_langchain_tools()
            
            # 转换为字典格式
            tool_list = []
            for tool in tools:
                tool_info = {
                    "name": tool.name,
                    "display_name": tool.name,
                    "description": tool.description,
                    "category": "general",  # LangChain工具暂时使用通用分类
                    "version": "1.0.0",
                    "author": "LangChain",
                    "icon": "🔧",
                    "tags": [],
                    "status": "available",
                    "inputs": tool.args_schema.model_json_schema() if tool.args_schema else {},
                    "outputs": {"type": "string", "description": "工具执行结果"},
                    "stats": self._get_tool_stats(tool.name)
                }
                tool_list.append(tool_info)
            
            return tool_list
            
        except Exception as e:
            logger.error(f"获取工具列表失败: {e}")
            return []
    
    async def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """
        获取LangChain工具详细信息
        
        Args:
            tool_name: 工具名称
            
        Returns:
            Optional[Dict[str, Any]]: 工具详细信息
        """
        try:
            tool = get_tool_by_name(tool_name)
            
            if not tool:
                return None
            
            return {
                "name": tool.name,
                "description": tool.description,
                "args_schema": tool.args_schema.model_json_schema() if tool.args_schema else {},
                "return_direct": tool.return_direct,
                "stats": self._get_tool_stats(tool_name)
            }
            
        except Exception as e:
            logger.error(f"获取工具信息失败: {e}")
            return None
    
    async def execute_tool(
        self,
        tool_name: str,
        inputs: Dict[str, Any],
        timeout: Optional[float] = None
    ) -> ToolCallInfo:
        """
        执行LangChain工具
        
        Args:
            tool_name: 工具名称
            inputs: 输入参数
            timeout: 超时时间
            
        Returns:
            ToolCallInfo: 工具调用信息
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"开始执行LangChain工具: {tool_name}")
            
            # 获取工具
            tool = get_tool_by_name(tool_name)
            
            if not tool:
                return create_tool_call_info(
                    tool_name=tool_name,
                    arguments=inputs,
                    result=f"工具不存在: {tool_name}",
                    success=False
                )
            
            # 执行工具
            if timeout:
                result = await asyncio.wait_for(
                    tool.ainvoke(inputs),
                    timeout=timeout
                )
            else:
                result = await tool.ainvoke(inputs)
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # 更新统计信息
            self._update_tool_stats(tool_name, True, execution_time)
            
            logger.info(f"工具执行成功: {tool_name}, 耗时: {execution_time:.2f}s")
            
            return create_tool_call_info(
                tool_name=tool_name,
                arguments=inputs,
                result=str(result),
                success=True,
                execution_time=execution_time
            )
            
        except asyncio.TimeoutError:
            error_msg = f"工具执行超时: {tool_name}"
            logger.error(error_msg)
            self._update_tool_stats(tool_name, False, 0.0)
            
            return create_tool_call_info(
                tool_name=tool_name,
                arguments=inputs,
                result=error_msg,
                success=False
            )
            
        except Exception as e:
            error_msg = f"工具执行失败: {str(e)}"
            logger.error(f"工具执行异常: {tool_name}, 错误: {e}")
            self._update_tool_stats(tool_name, False, 0.0)
            
            return create_tool_call_info(
                tool_name=tool_name,
                arguments=inputs,
                result=error_msg,
                success=False
            )
    
    def _get_tool_stats(self, tool_name: str) -> Dict[str, Any]:
        """获取工具统计信息"""
        stats = self._execution_stats.get(tool_name, {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "total_execution_time": 0.0,
            "average_execution_time": 0.0,
            "last_execution": None,
            "error_rate": 0.0
        })
        return stats
    
    def _update_tool_stats(self, tool_name: str, success: bool, execution_time: float):
        """更新工具统计信息"""
        if tool_name not in self._execution_stats:
            self._execution_stats[tool_name] = {
                "total_executions": 0,
                "successful_executions": 0,
                "failed_executions": 0,
                "total_execution_time": 0.0,
                "average_execution_time": 0.0,
                "last_execution": None,
                "error_rate": 0.0
            }
        
        stats = self._execution_stats[tool_name]
        stats["total_executions"] += 1
        stats["total_execution_time"] += execution_time
        stats["last_execution"] = datetime.now().isoformat()
        
        if success:
            stats["successful_executions"] += 1
        else:
            stats["failed_executions"] += 1
        
        # 计算平均执行时间和错误率
        if stats["total_executions"] > 0:
            stats["average_execution_time"] = stats["total_execution_time"] / stats["total_executions"]
            stats["error_rate"] = stats["failed_executions"] / stats["total_executions"]
    
    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        total_executions = sum(stats["total_executions"] for stats in self._execution_stats.values())
        total_successful = sum(stats["successful_executions"] for stats in self._execution_stats.values())
        
        return {
            "total_tools": len(get_all_langchain_tools()),
            "total_executions": total_executions,
            "successful_executions": total_successful,
            "failed_executions": total_executions - total_successful,
            "success_rate": total_successful / total_executions if total_executions > 0 else 0.0,
            "active_executions": len(self._running_executions),
            "tool_stats": self._execution_stats
        }


# 全局工具服务实例
tool_service = ToolService()


def get_tool_service() -> ToolService:
    """获取工具服务实例"""
    return tool_service
