#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IoT API v1 路由注册

注册知识库管理、文档管理、AI聊天和智能体相关的 API 路由
"""
from fastapi import APIRouter

from backend.app.iot.api.v1 import knowledge_base, document, ai_chat, tool_management, websocket_api, unified_chat

# 创建 IoT API v1 路由器
v1_router = APIRouter()

# 注册知识库管理路由
v1_router.include_router(knowledge_base.router, prefix='/knowledge-base', tags=['知识库管理'])

# 注册文档管理路由
v1_router.include_router(document.router, prefix='/documents', tags=['文档管理'])

# 注册统一聊天路由（推荐使用）
v1_router.include_router(unified_chat.router, prefix='/chat', tags=['统一聊天'])

# 注册AI聊天路由（兼容性保留）
v1_router.include_router(ai_chat.router, prefix='/ai', tags=['AI聊天'])

# 注册工具管理路由
v1_router.include_router(tool_management.router, prefix='/tools', tags=['工具管理'])

# 注册WebSocket路由
v1_router.include_router(websocket_api.router, prefix='/ws', tags=['WebSocket通信'])
