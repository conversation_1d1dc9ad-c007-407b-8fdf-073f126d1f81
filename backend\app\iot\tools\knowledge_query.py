#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库查询工具

使用LangChain标准@tool装饰器的知识库查询工具
"""
import httpx
import json
from typing import Optional
from loguru import logger
from langchain_core.tools import tool

from backend.app.iot.service.knowledge_base_service import knowledge_base_service


@tool
async def knowledge_query(
    query: str,
    knowledge_base_id: Optional[str] = None,
    top_k: int = 5,
    similarity_threshold: float = 0.7,
    search_type: str = "semantic"
) -> str:
    """
    在知识库中搜索相关文档和信息，支持语义搜索和关键词匹配

    Args:
        query: 要搜索的问题或关键词
        knowledge_base_id: 指定要搜索的知识库ID，不指定则搜索所有知识库
        top_k: 返回最相关的文档数量 (1-20)
        similarity_threshold: 文档相似度阈值 (0.0-1.0)
        search_type: 搜索类型，支持 semantic/keyword/hybrid

    Returns:
        str: 搜索结果
    """
    try:
        logger.info(f"执行知识库查询: {query}")

        # 验证参数
        if not query.strip():
            return "错误：查询内容不能为空"

        if not 1 <= top_k <= 20:
            return "错误：top_k 必须在 1-20 之间"

        if not 0.0 <= similarity_threshold <= 1.0:
            return "错误：similarity_threshold 必须在 0.0-1.0 之间"

        if search_type not in ["semantic", "keyword", "hybrid"]:
            return "错误：search_type 必须是 semantic、keyword 或 hybrid"

        # 执行搜索
        search_results = await _perform_knowledge_search(
            query=query,
            knowledge_base_id=knowledge_base_id,
            top_k=top_k,
            similarity_threshold=similarity_threshold,
            search_type=search_type
        )

        if not search_results:
            return f"未找到与 '{query}' 相关的文档"

        # 格式化结果
        result_text = f"找到 {len(search_results)} 个相关文档：\n\n"

        for i, doc in enumerate(search_results, 1):
            result_text += f"{i}. **{doc.get('title', '未知标题')}**\n"
            result_text += f"   相似度: {doc.get('similarity', 0):.2f}\n"
            result_text += f"   内容: {doc.get('content', '')[:200]}...\n"
            if doc.get('source'):
                result_text += f"   来源: {doc.get('source')}\n"
            result_text += "\n"

        return result_text

    except Exception as e:
        logger.error(f"知识库查询失败: {e}")
        return f"知识库查询失败: {str(e)}"


async def _perform_knowledge_search(
    query: str,
    knowledge_base_id: Optional[str] = None,
    top_k: int = 5,
    similarity_threshold: float = 0.7,
    search_type: str = "semantic"
) -> list:
    """执行实际的知识库搜索操作"""
    try:
        # 使用真实的RAGFlow知识库服务
        from backend.app.iot.schema.knowledge_base import KnowledgeRetrievalRequest

        # 如果没有指定知识库ID，获取所有可用的知识库
        dataset_ids = []
        if knowledge_base_id:
            dataset_ids = [knowledge_base_id]
        else:
            # 获取用户可访问的知识库列表
            try:
                kb_list_response = await knowledge_base_service.list_knowledge_bases(
                    query={"page": 1, "page_size": 100}
                )
                if kb_list_response.get("code") == 0:
                    kb_data = kb_list_response.get("data", [])
                    dataset_ids = [kb.get("id") for kb in kb_data if kb.get("id")]
            except Exception as e:
                logger.warning(f"获取知识库列表失败，使用默认配置: {e}")

        # 构建检索请求
        retrieval_request = KnowledgeRetrievalRequest(
            question=query,
            dataset_ids=dataset_ids if dataset_ids else None,
            top_k=top_k,
            similarity_threshold=similarity_threshold,
            keyword=search_type in ["keyword", "hybrid"],
            highlight=True
        )

        # 执行检索
        retrieval_response = await knowledge_base_service.retrieve_knowledge(retrieval_request)

        if retrieval_response.get("code") == 0:
            retrieval_data = retrieval_response.get("data", {})
            chunks = retrieval_data.get("chunks", [])

            # 转换为标准格式
            search_results = []
            for chunk in chunks:
                search_results.append({
                    "id": chunk.get("id"),
                    "title": f"文档片段 {chunk.get('id', '')[:8]}",
                    "content": chunk.get("content", ""),
                    "source": f"知识库文档 {chunk.get('document_id', '')}",
                    "similarity": chunk.get("similarity", 0.0),
                    "chunk_id": chunk.get("id"),
                    "document_id": chunk.get("document_id"),
                    "kb_id": chunk.get("kb_id"),
                    "important_keywords": chunk.get("important_keywords", []),
                    "positions": chunk.get("positions", [])
                })

            return search_results
        else:
            logger.error(f"RAGFlow检索失败: {retrieval_response.get('message', '未知错误')}")
            return []

    except Exception as e:
        logger.error(f"RAGFlow检索异常: {e}")
        # 如果RAGFlow服务不可用，返回空结果
        return []
